import { Request } from 'express';

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
}

export interface User {
  id: number;
  name: string;
  surname: string;
  email: string;
  password: string;
  profile_picture?: string;
  address?: Address;
  languages?: string[];
  birthday?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface UserPayload {
  id: number;
  email: string;
}

export interface AuthenticatedRequest extends Request {
  user?: UserPayload;
}

export interface RegisterRequest {
  name: string;
  surname: string;
  email: string;
  password: string;
  profile_picture?: string;
  address?: Address;
  languages?: string[];
  birthday?: string; // ISO date string
}

export interface UpdateProfileRequest {
  name?: string;
  surname?: string;
  profile_picture?: string;
  address?: Address;
  languages?: string[];
  birthday?: string; // ISO date string
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RouteConfig {
  requireAuth?: boolean;
}

export interface RouteHandler {
  handler: Function;
  config?: RouteConfig;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Utility function to calculate age from birthday
export const calculateAge = (birthday: Date): number => {
  const today = new Date();
  const birthDate = new Date(birthday);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
};
