import pool from '../config/database';
import { User, RegisterRequest, UpdateProfileRequest } from '../types';
import { hashPassword, comparePassword } from '../utils/password';

export class UserModel {
  static async create(userData: RegisterRequest): Promise<User> {
    const { name, surname, email, password, profile_picture, address, languages, birthday } = userData;

    // Hash the password
    const hashedPassword = await hashPassword(password);

    const query = `
      INSERT INTO users (name, surname, email, password, profile_picture, address, languages, birthday)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, name, surname, email, profile_picture, address, languages, birthday, created_at, updated_at
    `;

    const values = [
      name,
      surname,
      email,
      hashedPassword,
      profile_picture || null,
      address ? JSON.stringify(address) : null,
      languages || null,
      birthday ? new Date(birthday) : null
    ];
    const result = await pool.query(query, values);

    return result.rows[0];
  }

  static async findByEmail(email: string): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE email = $1';
    const result = await pool.query(query, [email]);
    
    return result.rows[0] || null;
  }

  static async findById(id: number): Promise<User | null> {
    const query = 'SELECT id, name, surname, email, profile_picture, address, languages, birthday, created_at, updated_at FROM users WHERE id = $1';
    const result = await pool.query(query, [id]);

    return result.rows[0] || null;
  }

  static async validatePassword(email: string, password: string): Promise<User | null> {
    const user = await this.findByEmail(email);
    
    if (!user) {
      return null;
    }
    
    const isValid = await comparePassword(password, user.password);
    
    if (!isValid) {
      return null;
    }
    
    // Return user without password
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  }

  static async updateLastLogin(id: number): Promise<void> {
    const query = 'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1';
    await pool.query(query, [id]);
  }

  static async emailExists(email: string): Promise<boolean> {
    const query = 'SELECT 1 FROM users WHERE email = $1';
    const result = await pool.query(query, [email]);

    return result.rows.length > 0;
  }

  static async updateProfile(id: number, profileData: UpdateProfileRequest): Promise<User | null> {
    const { name, surname, profile_picture, address, languages, birthday } = profileData;

    // Build dynamic query based on provided fields
    const updates: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (name !== undefined) {
      updates.push(`name = $${paramCount++}`);
      values.push(name);
    }
    if (surname !== undefined) {
      updates.push(`surname = $${paramCount++}`);
      values.push(surname);
    }
    if (profile_picture !== undefined) {
      updates.push(`profile_picture = $${paramCount++}`);
      values.push(profile_picture);
    }
    if (address !== undefined) {
      updates.push(`address = $${paramCount++}`);
      values.push(address ? JSON.stringify(address) : null);
    }
    if (languages !== undefined) {
      updates.push(`languages = $${paramCount++}`);
      values.push(languages);
    }
    if (birthday !== undefined) {
      updates.push(`birthday = $${paramCount++}`);
      values.push(birthday ? new Date(birthday) : null);
    }

    if (updates.length === 0) {
      // No updates provided, return current user
      return this.findById(id);
    }

    updates.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const query = `
      UPDATE users
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING id, name, surname, email, profile_picture, address, languages, birthday, created_at, updated_at
    `;

    const result = await pool.query(query, values);
    return result.rows[0] || null;
  }
}
