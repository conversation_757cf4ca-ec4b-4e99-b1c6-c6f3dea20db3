import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, calculateAge } from '../types';
import { UserModel } from '../models/User';

/**
 * @swagger
 * /api/me:
 *   get:
 *     tags: [User]
 *     summary: Get current user profile
 *     description: Retrieve the profile information of the currently authenticated user
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *                 message:
 *                   type: string
 *                   example: "User profile retrieved successfully"
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */


export const get = {
  handler: async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user) {
        const response: ApiResponse = {
          success: false,
          error: 'User not authenticated'
        };
        return res.status(401).json(response);
      }

      // Get user details from database
      const user = await UserModel.findById(req.user.id);
      
      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: 'User not found'
        };
        return res.status(404).json(response);
      }

      const response: ApiResponse = {
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.name,
            surname: user.surname,
            email: user.email,
            profile_picture: user.profile_picture,
            address: user.address,
            languages: user.languages,
            birthday: user.birthday,
            age: user.birthday ? calculateAge(user.birthday) : null,
            created_at: user.created_at,
            updated_at: user.updated_at
          }
        },
        message: 'User profile retrieved successfully'
      };

      return res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || 'Failed to retrieve user profile'
      };

      res.status(500).json(response);
    }
  },
  config: {
    requireAuth: true
  }
};
