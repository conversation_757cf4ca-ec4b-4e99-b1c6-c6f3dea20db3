import pool from '../config/database';
import dotenv from 'dotenv';

dotenv.config();

const migrateUserProfile = async () => {
  try {
    console.log('🔄 Running user profile migration...');
    
    // Check if columns already exist
    const checkColumns = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name IN ('profile_picture', 'address', 'languages', 'birthday')
    `);
    
    const existingColumns = checkColumns.rows.map(row => row.column_name);
    
    // Add profile_picture column if it doesn't exist
    if (!existingColumns.includes('profile_picture')) {
      await pool.query(`
        ALTER TABLE users 
        ADD COLUMN profile_picture TEXT
      `);
      console.log('✅ Added profile_picture column');
    } else {
      console.log('ℹ️  profile_picture column already exists');
    }
    
    // Add address column if it doesn't exist
    if (!existingColumns.includes('address')) {
      await pool.query(`
        ALTER TABLE users 
        ADD COLUMN address JSONB
      `);
      console.log('✅ Added address column');
    } else {
      console.log('ℹ️  address column already exists');
    }
    
    // Add languages column if it doesn't exist
    if (!existingColumns.includes('languages')) {
      await pool.query(`
        ALTER TABLE users 
        ADD COLUMN languages TEXT[]
      `);
      console.log('✅ Added languages column');
    } else {
      console.log('ℹ️  languages column already exists');
    }
    
    // Add birthday column if it doesn't exist
    if (!existingColumns.includes('birthday')) {
      await pool.query(`
        ALTER TABLE users 
        ADD COLUMN birthday DATE
      `);
      console.log('✅ Added birthday column');
    } else {
      console.log('ℹ️  birthday column already exists');
    }
    
    console.log('✅ User profile migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ User profile migration failed:', error);
    process.exit(1);
  }
};

migrateUserProfile();
