# Cheff-Up API

A TypeScript API with filepath-based routing, JWT authentication, and PostgreSQL database integration.

## Features

- 🚀 **Filepath-based routing**: Automatic route registration based on file structure
- 🔐 **JWT Authentication**: Secure token-based authentication
- 🛡️ **Middleware system**: Configurable authentication per route
- 📊 **Health checks**: Built-in health monitoring
- 🔒 **Security**: Helmet, CORS, rate limiting
- 📝 **Validation**: Request validation with Joi
- 🗄️ **PostgreSQL**: Database integration with connection pooling

## Quick Start

### Prerequisites

- Node.js (v18+)
- PostgreSQL database
- Bun package manager

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   bun install
   ```

3. Set up your environment variables (see `.env` file)

4. Run database migrations:
   ```bash
   bun run migrate
   ```

5. Start the development server:
   ```bash
   bun run dev
   ```

## API Endpoints

### Public Endpoints (No Authentication Required)

- `GET /api/health` - Health check
- `POST /api/register` - User registration
- `POST /api/login` - User login

### Protected Endpoints (Authentication Required)

- `GET /api/me` - Get current user profile

## Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Route Configuration

Routes support configuration options to control middleware behavior:

```typescript
export const get = {
  handler: async (req, res) => {
    // Your route logic
  },
  config: {
    requireAuth: false // Set to true to require authentication (default: true)
  }
};
```

## Project Structure

```
src/
├── config/          # Configuration files
├── middleware/      # Express middleware
├── models/          # Database models
├── routes/          # API route handlers
├── router/          # Filepath-based router
├── scripts/         # Utility scripts
├── types/           # TypeScript type definitions
└── utils/           # Utility functions
```

## Scripts

- `bun run dev` - Start development server with hot reload
- `bun run build` - Build for production
- `bun run start` - Start production server
- `bun run migrate` - Run database migrations
- `bun run test` - Run tests
- `bun run lint` - Run ESLint

## Environment Variables

See `.env` file for all available configuration options.

## License

MIT
