import Joi from 'joi';

const addressSchema = Joi.object({
  street: Joi.string().max(255).optional(),
  city: Joi.string().max(100).optional(),
  state: Joi.string().max(100).optional(),
  country: Joi.string().max(100).optional(),
  postal_code: Joi.string().max(20).optional()
});

export const registerSchema = Joi.object({
  name: Joi.string().min(2).max(100).required().messages({
    'string.min': 'Name must be at least 2 characters long',
    'string.max': 'Name must not exceed 100 characters',
    'any.required': 'Name is required'
  }),
  surname: Joi.string().min(2).max(100).required().messages({
    'string.min': 'Surname must be at least 2 characters long',
    'string.max': 'Surname must not exceed 100 characters',
    'any.required': 'Surname is required'
  }),
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]')).required().messages({
    'string.min': 'Password must be at least 8 characters long',
    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    'any.required': 'Password is required'
  }),
  profile_picture: Joi.string().uri().optional().messages({
    'string.uri': 'Profile picture must be a valid URL'
  }),
  address: addressSchema.optional(),
  languages: Joi.array().items(Joi.string().max(50)).max(10).optional().messages({
    'array.max': 'Maximum 10 languages allowed',
    'string.max': 'Language name must not exceed 50 characters'
  }),
  birthday: Joi.date().iso().max('now').optional().messages({
    'date.max': 'Birthday cannot be in the future',
    'date.format': 'Birthday must be a valid date in ISO format'
  })
});

export const loginSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().required().messages({
    'any.required': 'Password is required'
  })
});

export const updateProfileSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional().messages({
    'string.min': 'Name must be at least 2 characters long',
    'string.max': 'Name must not exceed 100 characters'
  }),
  surname: Joi.string().min(2).max(100).optional().messages({
    'string.min': 'Surname must be at least 2 characters long',
    'string.max': 'Surname must not exceed 100 characters'
  }),
  profile_picture: Joi.string().uri().allow('').optional().messages({
    'string.uri': 'Profile picture must be a valid URL'
  }),
  address: addressSchema.optional(),
  languages: Joi.array().items(Joi.string().max(50)).max(10).optional().messages({
    'array.max': 'Maximum 10 languages allowed',
    'string.max': 'Language name must not exceed 50 characters'
  }),
  birthday: Joi.date().iso().max('now').optional().messages({
    'date.max': 'Birthday cannot be in the future',
    'date.format': 'Birthday must be a valid date in ISO format'
  })
});

export const validateRequest = (schema: Joi.ObjectSchema, data: any) => {
  const { error, value } = schema.validate(data, { abortEarly: false });
  
  if (error) {
    const errors = error.details.map(detail => detail.message);
    throw new Error(errors.join(', '));
  }
  
  return value;
};
