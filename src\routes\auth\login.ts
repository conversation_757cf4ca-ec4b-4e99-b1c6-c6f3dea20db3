import { Request, Response } from 'express';
import { ApiResponse, LoginRequest, calculateAge } from '../../types';
import { UserModel } from '../../models/User';
import { validateRequest, loginSchema } from '../../utils/validation';
import { generateToken } from '../../utils/jwt';

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     tags: [Authentication]
 *     summary: User login
 *     description: Authenticate user with email and password
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *           example:
 *             email: "<EMAIL>"
 *             password: "SecurePass123!"
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// User login endpoint - no authentication required
export const post = {
  handler: async (req: Request, res: Response) => {
    try {
      // Validate request body
      const validatedData: LoginRequest = validateRequest(loginSchema, req.body);
      
      // Validate user credentials
      const user = await UserModel.validatePassword(validatedData.email, validatedData.password);
      
      if (!user) {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid email or password'
        };
        return res.status(401).json(response);
      }

      // Update last login timestamp
      await UserModel.updateLastLogin(user.id);
      
      // Generate JWT token
      const token = generateToken({
        id: user.id,
        email: user.email
      });

      const response: ApiResponse = {
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.name,
            surname: user.surname,
            email: user.email,
            profile_picture: user.profile_picture,
            address: user.address,
            languages: user.languages,
            birthday: user.birthday,
            age: user.birthday ? calculateAge(user.birthday) : null,
            created_at: user.created_at,
            updated_at: user.updated_at
          },
          token
        },
        message: 'Login successful'
      };

      return res.status(200).json(response);
    } catch (error: any) {
      const response: ApiResponse = {
        success: false,
        error: error.message || 'Login failed'
      };

      res.status(400).json(response);
    }
  },
  config: {
    requireAuth: false
  }
};
