import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, UpdateProfileRequest, calculateAge } from '../../types';
import { UserModel } from '../../models/User';
import { validateRequest, updateProfileSchema } from '../../utils/validation';

export const get = {
    handler: async (req: AuthenticatedRequest, res: Response) => {
        try {
            if (!req.user) {
                const response: ApiResponse = {
                    success: false,
                    error: 'User not authenticated'
                };
                return res.status(401).json(response);
            }

            // Get user details from database
            const user = await UserModel.findById(req.user.id);

            if (!user) {
                const response: ApiResponse = {
                    success: false,
                    error: 'User not found'
                };
                return res.status(404).json(response);
            }

            const response: ApiResponse = {
                success: true,
                data: {
                    user: {
                        id: user.id,
                        name: user.name,
                        surname: user.surname,
                        email: user.email,
                        profile_picture: user.profile_picture,
                        address: user.address,
                        languages: user.languages,
                        birthday: user.birthday,
                        age: user.birthday ? calculateAge(user.birthday) : null,
                        created_at: user.created_at,
                        updated_at: user.updated_at
                    }
                },
                message: 'User profile retrieved successfully'
            };

            return res.status(200).json(response);
        } catch (error: any) {
            const response: ApiResponse = {
                success: false,
                error: error.message || 'Failed to retrieve user profile'
            };

            return res.status(500).json(response);
        }
    },
    config: {
        requireAuth: true
    }
}

export const put = {
    handler: async (req: AuthenticatedRequest, res: Response) => {
        try {
            if (!req.user) {
                const response: ApiResponse = {
                    success: false,
                    error: 'User not authenticated'
                };
                return res.status(401).json(response);
            }

            // Validate request body
            const validatedData: UpdateProfileRequest = validateRequest(updateProfileSchema, req.body);

            // Update user profile
            const updatedUser = await UserModel.updateProfile(req.user.id, validatedData);

            if (!updatedUser) {
                const response: ApiResponse = {
                    success: false,
                    error: 'User not found'
                };
                return res.status(404).json(response);
            }

            const response: ApiResponse = {
                success: true,
                data: {
                    user: {
                        id: updatedUser.id,
                        name: updatedUser.name,
                        surname: updatedUser.surname,
                        email: updatedUser.email,
                        profile_picture: updatedUser.profile_picture,
                        address: updatedUser.address,
                        languages: updatedUser.languages,
                        birthday: updatedUser.birthday,
                        age: updatedUser.birthday ? calculateAge(updatedUser.birthday) : null,
                        created_at: updatedUser.created_at,
                        updated_at: updatedUser.updated_at
                    }
                },
                message: 'Profile updated successfully'
            };

            return res.status(200).json(response);
        } catch (error: any) {
            const response: ApiResponse = {
                success: false,
                error: error.message || 'Failed to update profile'
            };

            return res.status(400).json(response);
        }
    },
    config: {
        requireAuth: true
    }
}
